<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Swatch Legend</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>Color Swatch Legend</h1>
            <p>Create interactive color legends from selected swatches</p>
        </header>

        <!-- Settings Panel -->
        <div class="settings-panel">
            <h2>Settings</h2>

            <!-- Layout Settings -->
            <div class="setting-group">
                <h3>Grid Layout</h3>
                <div class="input-group">
                    <label for="columns">Columns:</label>
                    <input type="number" id="columns" value="4" min="1" max="10">
                </div>
                <div class="input-group">
                    <label for="width">Width (px):</label>
                    <input type="number" id="width" value="150" min="50" max="300">
                </div>
                <div class="input-group">
                    <label for="height">Height (px):</label>
                    <input type="number" id="height" value="120" min="50" max="300">
                </div>
            </div>

            <!-- Color Format Settings -->
            <div class="setting-group">
                <h3>Color Formats</h3>
                <div class="checkbox-group">
                    <label><input type="checkbox" id="showHEX" checked> HEX</label>
                    <label><input type="checkbox" id="showRGB" checked> RGB</label>
                    <label><input type="checkbox" id="showCMYK" checked> CMYK</label>
                    <label><input type="checkbox" id="showLAB" checked> LAB</label>
                    <label><input type="checkbox" id="showGray" checked> GrayScale</label>
                </div>
            </div>

            <!-- Text Settings -->
            <div class="setting-group">
                <h3>Text Settings</h3>
                <div class="input-group">
                    <label for="textSize">Text Size:</label>
                    <input type="number" id="textSize" value="10" min="6" max="24">
                </div>
                <div class="input-group">
                    <label for="colorSeparator">Color Separator:</label>
                    <input type="text" id="colorSeparator" value=" " maxlength="3">
                </div>
                <div class="checkbox-group">
                    <label><input type="checkbox" id="splitComponents"> Split Color Components</label>
                </div>
            </div>

            <!-- Advanced Settings -->
            <div class="setting-group">
                <h3>Advanced Settings</h3>
                <div class="input-group">
                    <label for="hPadding">Horizontal Padding:</label>
                    <input type="number" id="hPadding" value="10" min="0" max="50">
                </div>
                <div class="input-group">
                    <label for="vPadding">Vertical Padding:</label>
                    <input type="number" id="vPadding" value="10" min="0" max="50">
                </div>
            </div>
        </div>

        <!-- Preview Panel -->
        <div class="preview-panel">
            <h2>Preview</h2>
            <div id="previewArea" class="preview-area">
                <p>Select swatches in Illustrator to see preview</p>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <button id="previewBtn" class="btn btn-secondary" title="Ctrl+R">Preview</button>
            <button id="generateBtn" class="btn btn-primary" title="Ctrl+G">Generate Legend</button>
            <button id="exportBtn" class="btn btn-success" title="Ctrl+E">Export Settings</button>
            <button id="importBtn" class="btn btn-info" title="Ctrl+I">Import Settings</button>
        </div>

        <!-- Advanced Features -->
        <div class="advanced-features">
            <h2>Advanced Features</h2>
            <div class="feature-buttons">
                <button id="reportBtn" class="btn btn-outline">Color Report</button>
                <button id="copyBtn" class="btn btn-outline">Copy Values</button>
                <button id="resetBtn" class="btn btn-warning">Reset</button>
                <button id="duplicateBtn" class="btn btn-outline">Duplicate Settings</button>
            </div>

            <!-- Quick Settings -->
            <div class="quick-settings">
                <h3>Quick Settings</h3>
                <div class="preset-buttons">
                    <button class="preset-btn" data-preset="compact">Compact</button>
                    <button class="preset-btn" data-preset="detailed">Detailed</button>
                    <button class="preset-btn" data-preset="minimal">Minimal</button>
                    <button class="preset-btn" data-preset="professional">Professional</button>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <span id="statusText">Ready</span>
            <div id="progressBar" class="progress-bar" style="display: none;">
                <div class="progress-fill"></div>
            </div>
        </div>
    </div>

    <script>
        console.log("Loading Color Swatch Legend Extension...");

        // Simple CSInterface fallback for testing
        if (typeof CSInterface === 'undefined') {
            console.log("CSInterface not found, creating fallback...");
            window.CSInterface = function() {
                this.THEME_COLOR_CHANGED_EVENT = "com.adobe.csxs.events.ThemeColorChanged";
            };

            CSInterface.prototype.evalScript = function(script, callback) {
                console.log("CSInterface.evalScript called (fallback mode)");
                // Simulate async operation
                setTimeout(() => {
                    if (callback) callback("[]");
                }, 100);
            };

            CSInterface.prototype.getSystemPath = function(pathType) {
                return "";
            };

            CSInterface.prototype.getApplicationID = function() {
                return "ILST";
            };

            CSInterface.prototype.getHostEnvironment = function() {
                return JSON.stringify({
                    appName: "ILST",
                    appVersion: "25.0.0"
                });
            };
        } else {
            console.log("CSInterface found, using native implementation");
        }

        // Check if DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM loaded, initializing extension...");
        });
    </script>
    <script src="js/main.js"></script>
</body>
</html>
