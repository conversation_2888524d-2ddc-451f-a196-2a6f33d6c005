<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Swatch Legend</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>مولد مخطط الألوان</h1>
            <p>إنشاء مخطط تفاعلي للألوان من العينات المحددة</p>
        </header>

        <!-- Settings Panel -->
        <div class="settings-panel">
            <h2>الإعدادات</h2>
            
            <!-- Layout Settings -->
            <div class="setting-group">
                <h3>تخطيط الشبكة</h3>
                <div class="input-group">
                    <label for="columns">عدد الأعمدة:</label>
                    <input type="number" id="columns" value="4" min="1" max="10">
                </div>
                <div class="input-group">
                    <label for="width">العرض (px):</label>
                    <input type="number" id="width" value="150" min="50" max="300">
                </div>
                <div class="input-group">
                    <label for="height">الارتفاع (px):</label>
                    <input type="number" id="height" value="120" min="50" max="300">
                </div>
            </div>

            <!-- Color Format Settings -->
            <div class="setting-group">
                <h3>صيغ الألوان</h3>
                <div class="checkbox-group">
                    <label><input type="checkbox" id="showHEX" checked> HEX</label>
                    <label><input type="checkbox" id="showRGB" checked> RGB</label>
                    <label><input type="checkbox" id="showCMYK" checked> CMYK</label>
                    <label><input type="checkbox" id="showLAB" checked> LAB</label>
                    <label><input type="checkbox" id="showGray" checked> GrayScale</label>
                </div>
            </div>

            <!-- Text Settings -->
            <div class="setting-group">
                <h3>إعدادات النص</h3>
                <div class="input-group">
                    <label for="textSize">حجم النص:</label>
                    <input type="number" id="textSize" value="10" min="6" max="24">
                </div>
                <div class="input-group">
                    <label for="colorSeparator">فاصل الألوان:</label>
                    <input type="text" id="colorSeparator" value=" " maxlength="3">
                </div>
                <div class="checkbox-group">
                    <label><input type="checkbox" id="splitComponents"> تقسيم مكونات اللون</label>
                </div>
            </div>

            <!-- Advanced Settings -->
            <div class="setting-group">
                <h3>إعدادات متقدمة</h3>
                <div class="input-group">
                    <label for="hPadding">المسافة الأفقية:</label>
                    <input type="number" id="hPadding" value="10" min="0" max="50">
                </div>
                <div class="input-group">
                    <label for="vPadding">المسافة العمودية:</label>
                    <input type="number" id="vPadding" value="10" min="0" max="50">
                </div>
            </div>
        </div>

        <!-- Preview Panel -->
        <div class="preview-panel">
            <h2>معاينة</h2>
            <div id="previewArea" class="preview-area">
                <p>اختر العينات في Illustrator لرؤية المعاينة</p>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <button id="previewBtn" class="btn btn-secondary" title="Ctrl+R">معاينة</button>
            <button id="generateBtn" class="btn btn-primary" title="Ctrl+G">إنشاء المخطط</button>
            <button id="exportBtn" class="btn btn-success" title="Ctrl+E">تصدير الإعدادات</button>
            <button id="importBtn" class="btn btn-info" title="Ctrl+I">استيراد الإعدادات</button>
        </div>

        <!-- Advanced Features -->
        <div class="advanced-features">
            <h2>ميزات متقدمة</h2>
            <div class="feature-buttons">
                <button id="reportBtn" class="btn btn-outline">تقرير الألوان</button>
                <button id="copyBtn" class="btn btn-outline">نسخ القيم</button>
                <button id="resetBtn" class="btn btn-warning">إعادة تعيين</button>
                <button id="duplicateBtn" class="btn btn-outline">تكرار الإعدادات</button>
            </div>

            <!-- Quick Settings -->
            <div class="quick-settings">
                <h3>إعدادات سريعة</h3>
                <div class="preset-buttons">
                    <button class="preset-btn" data-preset="compact">مضغوط</button>
                    <button class="preset-btn" data-preset="detailed">مفصل</button>
                    <button class="preset-btn" data-preset="minimal">بسيط</button>
                    <button class="preset-btn" data-preset="professional">احترافي</button>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <span id="statusText">جاهز</span>
            <div id="progressBar" class="progress-bar" style="display: none;">
                <div class="progress-fill"></div>
            </div>
        </div>
    </div>

    <script>
        // Simple CSInterface fallback for testing
        if (typeof CSInterface === 'undefined') {
            window.CSInterface = function() {
                this.THEME_COLOR_CHANGED_EVENT = "com.adobe.csxs.events.ThemeColorChanged";
            };

            CSInterface.prototype.evalScript = function(script, callback) {
                // Simulate async operation
                setTimeout(() => {
                    if (callback) callback("[]");
                }, 100);
            };

            CSInterface.prototype.getSystemPath = function(pathType) {
                return "";
            };

            CSInterface.prototype.getApplicationID = function() {
                return "ILST";
            };

            CSInterface.prototype.getHostEnvironment = function() {
                return JSON.stringify({
                    appName: "ILST",
                    appVersion: "25.0.0"
                });
            };
        }
    </script>
    <script src="js/main.js"></script>
</body>
</html>
