<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Color Swatch Legend</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #005a87;
        }
        .preview-area {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            min-height: 100px;
            background: #fafafa;
        }
        .status-bar {
            background: #e0e0e0;
            padding: 10px;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Color Swatch Legend</h1>
        <p>Create interactive color legends from selected swatches</p>


        <h2>Settings</h2>
        <div>
            <label>Columns: <input type="number" id="columns" value="4" min="1" max="10"></label><br><br>
            <label>Width: <input type="number" id="width" value="150" min="50" max="300"></label><br><br>
            <label>Height: <input type="number" id="height" value="120" min="50" max="300"></label><br><br>
        </div>

        <h2>Preview</h2>
        <div id="previewArea" class="preview-area">
            <p>Click Preview to see sample colors</p>
        </div>

        <div>
            <button id="previewBtn" class="btn">Preview</button>
            <button id="generateBtn" class="btn">Generate Legend</button>
        </div>

        <div class="status-bar">
            <span id="statusText">Ready</span>
        </div>
    </div>

    <script>
        console.log("Loading Color Swatch Legend Extension...");

        // Simple test functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM loaded, setting up basic functionality...");

            // Test data
            const testColors = [
                { name: "Red", color: { type: "RGB", r: 255, g: 0, b: 0 } },
                { name: "Blue", color: { type: "RGB", r: 0, g: 0, b: 255 } },
                { name: "Green", color: { type: "RGB", r: 0, g: 255, b: 0 } },
                { name: "Yellow", color: { type: "RGB", r: 255, g: 255, b: 0 } }
            ];

            // Preview button
            document.getElementById('previewBtn').addEventListener('click', function() {
                console.log("Preview button clicked");
                const previewArea = document.getElementById('previewArea');
                let html = '<h3>Sample Colors:</h3>';

                testColors.forEach(color => {
                    const rgb = `rgb(${color.color.r}, ${color.color.g}, ${color.color.b})`;
                    html += `<div style="display: inline-block; margin: 5px; padding: 10px; background: ${rgb}; color: white; border-radius: 4px;">
                        ${color.name}<br>
                        RGB(${color.color.r}, ${color.color.g}, ${color.color.b})
                    </div>`;
                });

                previewArea.innerHTML = html;
                document.getElementById('statusText').textContent = `Loaded ${testColors.length} test colors`;
            });

            // Generate button
            document.getElementById('generateBtn').addEventListener('click', function() {
                console.log("Generate button clicked");
                alert("Test Mode: In real use, this would create a legend in Adobe Illustrator");
                document.getElementById('statusText').textContent = "Legend created (Test Mode)";
            });

            document.getElementById('statusText').textContent = "Extension loaded successfully";
            console.log("Extension setup complete");
        });
    </script>
</body>
</html>
