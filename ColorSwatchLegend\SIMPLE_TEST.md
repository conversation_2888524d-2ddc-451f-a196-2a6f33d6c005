# Simple Test Version

## What Changed
Created a completely simplified version of the extension to fix the blank screen issue:

### ✅ Simplifications Applied:
1. **Single HTML file** - All CSS and JavaScript inline
2. **No external dependencies** - No separate CSS or JS files
3. **Basic styling** - Simple, clean interface
4. **Test functionality** - Working preview with sample colors
5. **Console logging** - For debugging
6. **Minimal code** - Only essential features

## Current File Structure:
```
ColorSwatchLegend/
├── index.html          (Simplified, self-contained)
├── CSXS/manifest.xml   (Extension config)
└── Other files...      (Not used in simple version)
```

## Test Steps:

### 1. Close and Restart Illustrator
```
1. Close Adobe Illustrator completely
2. Restart Illustrator
3. Go to: Window > Extensions > Color Swatch Legend
```

### 2. Check Extension Loading
- Extension panel should open
- Should show: "Color Swatch Legend" title
- Should show: Settings section with inputs
- Should show: Preview section
- Should show: Two buttons (Preview, Generate Legend)
- Status should show: "Extension loaded successfully"

### 3. Test Preview Function
```
1. Click "Preview" button
2. Should show 4 colored squares (<PERSON>, <PERSON>, Green, Yellow)
3. Status should update to: "Loaded 4 test colors"
```

### 4. Test Generate Function
```
1. Click "Generate Legend" button
2. Should show alert: "Test Mode: In real use, this would create a legend in Adobe Illustrator"
3. Status should update to: "Legend created (Test Mode)"
```

### 5. Check Console (F12)
Expected console messages:
```
Loading Color Swatch Legend Extension...
DOM loaded, setting up basic functionality...
Extension setup complete
Preview button clicked (when clicking Preview)
Generate button clicked (when clicking Generate)
```

## Success Indicators:

### ✅ Extension Works If:
- [ ] Panel opens and shows content
- [ ] Title and text are visible
- [ ] Input fields are present and functional
- [ ] Preview button shows colored squares
- [ ] Generate button shows alert
- [ ] Status bar updates correctly
- [ ] No errors in console

### ❌ Still Has Issues If:
- [ ] Panel is blank/empty
- [ ] Buttons don't respond
- [ ] Console shows errors
- [ ] Status doesn't update

## Troubleshooting:

### If Still Blank:
1. **Check Console (F12)** for JavaScript errors
2. **Verify file path** - Make sure index.html is in correct location
3. **Check manifest.xml** - Ensure MainPath points to ./index.html
4. **Restart Illustrator** - Sometimes requires full restart
5. **Check PlayerDebugMode** - Must be enabled

### Common Issues:
- **File permissions** - Ensure read access to all files
- **Path separators** - Use forward slashes in manifest.xml
- **Cache issues** - Clear browser cache or restart Illustrator

## Next Steps:

### If This Works:
1. We can gradually add back features
2. Integrate with real Adobe Illustrator APIs
3. Add the full interface back
4. Connect to JSX backend

### If This Still Fails:
1. Check Adobe Illustrator version compatibility
2. Verify CEP version requirements
3. Test with different manifest settings
4. Consider alternative extension structure

---

**This is the simplest possible version that should work. If this doesn't display content, the issue is with the basic extension setup, not the code complexity.**
