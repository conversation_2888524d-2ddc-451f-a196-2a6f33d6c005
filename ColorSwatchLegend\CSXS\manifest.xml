<?xml version="1.0" encoding="UTF-8"?>
<ExtensionManifest Version="7.0" ExtensionBundleId="com.colorswatchlegend.extension" ExtensionBundleVersion="1.0.0"
                  ExtensionBundleName="Color Swatch Legend" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <ExtensionList>
    <Extension Id="com.colorswatchlegend.panel" Version="1.0.0"/>
  </ExtensionList>
  <ExecutionEnvironment>
    <HostList>
      <Host Name="ILST" Version="[22.0,99.9]"/>
    </HostList>
    <LocaleList>
      <Locale Code="All"/>
    </LocaleList>
    <RequiredRuntimeList>
      <RequiredRuntime Name="CSXS" Version="9.0"/>
    </RequiredRuntimeList>
  </ExecutionEnvironment>
  <DispatchInfoList>
    <Extension Id="com.colorswatchlegend.panel">
      <DispatchInfo>
        <Resources>
          <MainPath>./index.html</MainPath>
          <ScriptPath>./jsx/colorSwatchLegend.jsx</ScriptPath>
        </Resources>
        <Lifecycle>
          <AutoVisible>true</AutoVisible>
        </Lifecycle>
        <UI>
          <Type>Panel</Type>
          <Menu>Color Swatch Legend</Menu>
          <Geometry>
            <Size>
              <Width>400</Width>
              <Height>600</Height>
            </Size>
            <MinSize>
              <Width>300</Width>
              <Height>400</Height>
            </MinSize>
            <MaxSize>
              <Width>800</Width>
              <Height>1000</Height>
            </MaxSize>
          </Geometry>
          <Icons>
            <Icon Type="Normal">./icons/icon-normal.png</Icon>
            <Icon Type="RollOver">./icons/icon-rollover.png</Icon>
            <Icon Type="DarkNormal">./icons/icon-dark-normal.png</Icon>
            <Icon Type="DarkRollOver">./icons/icon-dark-rollover.png</Icon>
          </Icons>
        </UI>
      </DispatchInfo>
    </Extension>
  </DispatchInfoList>
</ExtensionManifest>
