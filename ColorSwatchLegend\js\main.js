/**
 * Color Swatch Legend Extension - Main JavaScript
 * Interactive UI Controller
 */

class ColorSwatchLegendUI {
    constructor() {
        this.csInterface = new CSInterface();
        this.settings = this.getDefaultSettings();
        this.selectedSwatches = [];
        this.init();
    }

    getDefaultSettings() {
        return {
            columns: 4,
            width: 150,
            height: 120,
            textSize: 10,
            colorSeparator: " ",
            splitComponents: false,
            hPadding: 10,
            vPadding: 10,
            printColors: {
                HEX: true,
                RGB: true,
                CMYK: true,
                LAB: true,
                GrayScale: true
            }
        };
    }

    init() {
        this.bindEvents();
        this.loadSettings();
        this.updateUI();
        this.setStatus("جاهز للاستخدام");
    }

    bindEvents() {
        // Settings change events
        document.getElementById('columns').addEventListener('input', (e) => {
            this.settings.columns = parseInt(e.target.value);
            this.updatePreview();
        });

        document.getElementById('width').addEventListener('input', (e) => {
            this.settings.width = parseInt(e.target.value);
            this.updatePreview();
        });

        document.getElementById('height').addEventListener('input', (e) => {
            this.settings.height = parseInt(e.target.value);
            this.updatePreview();
        });

        document.getElementById('textSize').addEventListener('input', (e) => {
            this.settings.textSize = parseInt(e.target.value);
            this.updatePreview();
        });

        document.getElementById('colorSeparator').addEventListener('input', (e) => {
            this.settings.colorSeparator = e.target.value;
            this.updatePreview();
        });

        document.getElementById('splitComponents').addEventListener('change', (e) => {
            this.settings.splitComponents = e.target.checked;
            this.updatePreview();
        });

        document.getElementById('hPadding').addEventListener('input', (e) => {
            this.settings.hPadding = parseInt(e.target.value);
            this.updatePreview();
        });

        document.getElementById('vPadding').addEventListener('input', (e) => {
            this.settings.vPadding = parseInt(e.target.value);
            this.updatePreview();
        });

        // Color format checkboxes
        ['showHEX', 'showRGB', 'showCMYK', 'showLAB', 'showGray'].forEach(id => {
            document.getElementById(id).addEventListener('change', (e) => {
                const colorType = id.replace('show', '').replace('Gray', 'GrayScale');
                this.settings.printColors[colorType] = e.target.checked;
                this.updatePreview();
            });
        });

        // Action buttons
        document.getElementById('previewBtn').addEventListener('click', () => {
            this.previewSwatches();
        });

        document.getElementById('generateBtn').addEventListener('click', () => {
            this.generateLegend();
        });

        document.getElementById('exportBtn').addEventListener('click', () => {
            this.exportSettings();
        });

        document.getElementById('importBtn').addEventListener('click', () => {
            this.importSettings();
        });

        // Advanced feature buttons
        document.getElementById('reportBtn').addEventListener('click', () => {
            this.generateColorReport();
        });

        document.getElementById('copyBtn').addEventListener('click', () => {
            this.copyColorValues();
        });

        document.getElementById('resetBtn').addEventListener('click', () => {
            this.resetToDefaults();
        });

        document.getElementById('duplicateBtn').addEventListener('click', () => {
            this.duplicateSettings();
        });

        // Preset buttons
        document.querySelectorAll('.preset-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.applyPreset(e.target.dataset.preset);
                // Update active state
                document.querySelectorAll('.preset-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
            });
        });
    }

    updateUI() {
        // Update input values
        document.getElementById('columns').value = this.settings.columns;
        document.getElementById('width').value = this.settings.width;
        document.getElementById('height').value = this.settings.height;
        document.getElementById('textSize').value = this.settings.textSize;
        document.getElementById('colorSeparator').value = this.settings.colorSeparator;
        document.getElementById('splitComponents').checked = this.settings.splitComponents;
        document.getElementById('hPadding').value = this.settings.hPadding;
        document.getElementById('vPadding').value = this.settings.vPadding;

        // Update checkboxes
        Object.keys(this.settings.printColors).forEach(colorType => {
            const id = 'show' + (colorType === 'GrayScale' ? 'Gray' : colorType);
            const checkbox = document.getElementById(id);
            if (checkbox) {
                checkbox.checked = this.settings.printColors[colorType];
            }
        });
    }

    previewSwatches() {
        this.setStatus("جاري تحميل العينات...");
        this.showProgress(true);

        const script = `
            try {
                var doc = app.activeDocument;
                var swatches = doc.swatches.getSelected();
                var result = [];
                
                for (var i = 0; i < swatches.length; i++) {
                    var swatch = swatches[i];
                    var colorInfo = {
                        name: swatch.name,
                        color: null
                    };
                    
                    if (swatch.color.typename) {
                        switch (swatch.color.typename) {
                            case "RGBColor":
                                colorInfo.color = {
                                    type: "RGB",
                                    r: Math.round(swatch.color.red),
                                    g: Math.round(swatch.color.green),
                                    b: Math.round(swatch.color.blue)
                                };
                                break;
                            case "CMYKColor":
                                colorInfo.color = {
                                    type: "CMYK",
                                    c: Math.round(swatch.color.cyan),
                                    m: Math.round(swatch.color.magenta),
                                    y: Math.round(swatch.color.yellow),
                                    k: Math.round(swatch.color.black)
                                };
                                break;
                            case "GrayColor":
                                colorInfo.color = {
                                    type: "Gray",
                                    gray: Math.round(swatch.color.gray)
                                };
                                break;
                        }
                    }
                    
                    if (colorInfo.color) {
                        result.push(colorInfo);
                    }
                }
                
                JSON.stringify(result);
            } catch (e) {
                "Error: " + e.toString();
            }
        `;

        this.csInterface.evalScript(script, (result) => {
            this.showProgress(false);
            
            if (result.startsWith("Error:")) {
                this.setStatus("خطأ: " + result.substring(6));
                return;
            }

            try {
                this.selectedSwatches = JSON.parse(result);
                this.updatePreview();
                this.setStatus(`تم تحميل ${this.selectedSwatches.length} عينة`);
            } catch (e) {
                this.setStatus("خطأ في تحليل البيانات");
            }
        });
    }

    updatePreview() {
        const previewArea = document.getElementById('previewArea');
        
        if (this.selectedSwatches.length === 0) {
            previewArea.innerHTML = '<p>اختر العينات في Illustrator لرؤية المعاينة</p>';
            return;
        }

        const gridStyle = `
            grid-template-columns: repeat(${this.settings.columns}, 1fr);
            gap: ${this.settings.hPadding}px;
        `;

        let previewHTML = `<div class="preview-grid" style="${gridStyle}">`;

        this.selectedSwatches.forEach(swatch => {
            const colorStyle = this.getColorStyle(swatch.color);
            const colorText = this.getColorText(swatch.color);
            
            previewHTML += `
                <div class="preview-swatch" style="width: ${this.settings.width}px;">
                    <div class="preview-color" style="background: ${colorStyle}; height: ${this.settings.height * 0.6}px;"></div>
                    <div class="preview-text" style="font-size: ${this.settings.textSize}px;">
                        <strong>${swatch.name}</strong><br>
                        ${colorText}
                    </div>
                </div>
            `;
        });

        previewHTML += '</div>';
        previewArea.innerHTML = previewHTML;
    }

    getColorStyle(color) {
        if (!color) return '#cccccc';
        
        switch (color.type) {
            case 'RGB':
                return `rgb(${color.r}, ${color.g}, ${color.b})`;
            case 'CMYK':
                // Convert CMYK to RGB for preview
                const r = Math.round(255 * (1 - color.c / 100) * (1 - color.k / 100));
                const g = Math.round(255 * (1 - color.m / 100) * (1 - color.k / 100));
                const b = Math.round(255 * (1 - color.y / 100) * (1 - color.k / 100));
                return `rgb(${r}, ${g}, ${b})`;
            case 'Gray':
                const gray = Math.round(255 * (1 - color.gray / 100));
                return `rgb(${gray}, ${gray}, ${gray})`;
            default:
                return '#cccccc';
        }
    }

    getColorText(color) {
        if (!color) return '';
        
        const activeFormats = Object.keys(this.settings.printColors)
            .filter(format => this.settings.printColors[format]);
        
        let text = '';
        
        activeFormats.forEach(format => {
            switch (format) {
                case 'RGB':
                    if (color.type === 'RGB') {
                        text += `RGB: ${color.r}, ${color.g}, ${color.b}<br>`;
                    }
                    break;
                case 'CMYK':
                    if (color.type === 'CMYK') {
                        text += `CMYK: ${color.c}, ${color.m}, ${color.y}, ${color.k}<br>`;
                    }
                    break;
                case 'HEX':
                    if (color.type === 'RGB') {
                        const hex = '#' + 
                            color.r.toString(16).padStart(2, '0') +
                            color.g.toString(16).padStart(2, '0') +
                            color.b.toString(16).padStart(2, '0');
                        text += `HEX: ${hex.toUpperCase()}<br>`;
                    }
                    break;
            }
        });
        
        return text;
    }

    generateLegend() {
        if (this.selectedSwatches.length === 0) {
            this.setStatus("يرجى اختيار العينات أولاً");
            return;
        }

        this.setStatus("جاري إنشاء المخطط...");
        this.showProgress(true);

        const settingsJSON = JSON.stringify(this.settings);
        const script = `
            try {
                var settings = ${settingsJSON};
                // Call the JSX function with settings
                generateColorSwatchLegend(settings);
                "تم إنشاء المخطط بنجاح";
            } catch (e) {
                "Error: " + e.toString();
            }
        `;

        this.csInterface.evalScript(script, (result) => {
            this.showProgress(false);
            
            if (result.startsWith("Error:")) {
                this.setStatus("خطأ: " + result.substring(6));
            } else {
                this.setStatus(result);
            }
        });
    }

    exportSettings() {
        const dataStr = JSON.stringify(this.settings, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = 'color-swatch-settings.json';
        link.click();
        
        this.setStatus("تم تصدير الإعدادات");
    }

    importSettings() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const importedSettings = JSON.parse(e.target.result);
                        this.settings = { ...this.getDefaultSettings(), ...importedSettings };
                        this.updateUI();
                        this.updatePreview();
                        this.setStatus("تم استيراد الإعدادات");
                    } catch (error) {
                        this.setStatus("خطأ في قراءة الملف");
                    }
                };
                reader.readAsText(file);
            }
        };
        
        input.click();
    }

    loadSettings() {
        const saved = localStorage.getItem('colorSwatchLegendSettings');
        if (saved) {
            try {
                this.settings = { ...this.getDefaultSettings(), ...JSON.parse(saved) };
            } catch (e) {
                console.warn('Failed to load saved settings');
            }
        }
    }

    saveSettings() {
        localStorage.setItem('colorSwatchLegendSettings', JSON.stringify(this.settings));
    }

    setStatus(message) {
        document.getElementById('statusText').textContent = message;
    }

    showProgress(show) {
        const progressBar = document.getElementById('progressBar');
        progressBar.style.display = show ? 'block' : 'none';
    }

    // Advanced Features

    duplicateSettings() {
        const newSettings = JSON.parse(JSON.stringify(this.settings));
        newSettings.columns = Math.min(newSettings.columns + 1, 10);
        this.settings = newSettings;
        this.updateUI();
        this.updatePreview();
        this.setStatus("تم تكرار الإعدادات مع تعديل");
    }

    resetToDefaults() {
        if (confirm("هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟")) {
            this.settings = this.getDefaultSettings();
            this.updateUI();
            this.updatePreview();
            this.setStatus("تم إعادة تعيين الإعدادات");
        }
    }

    generateColorReport() {
        if (this.selectedSwatches.length === 0) {
            this.setStatus("يرجى اختيار العينات أولاً");
            return;
        }

        let report = "تقرير الألوان\n";
        report += "================\n\n";
        report += `عدد الألوان: ${this.selectedSwatches.length}\n`;
        report += `تاريخ الإنشاء: ${new Date().toLocaleString('ar')}\n\n`;

        this.selectedSwatches.forEach((swatch, index) => {
            report += `${index + 1}. ${swatch.name}\n`;
            if (swatch.color) {
                switch (swatch.color.type) {
                    case 'RGB':
                        report += `   RGB: ${swatch.color.r}, ${swatch.color.g}, ${swatch.color.b}\n`;
                        const hex = '#' +
                            swatch.color.r.toString(16).padStart(2, '0') +
                            swatch.color.g.toString(16).padStart(2, '0') +
                            swatch.color.b.toString(16).padStart(2, '0');
                        report += `   HEX: ${hex.toUpperCase()}\n`;
                        break;
                    case 'CMYK':
                        report += `   CMYK: ${swatch.color.c}%, ${swatch.color.m}%, ${swatch.color.y}%, ${swatch.color.k}%\n`;
                        break;
                    case 'Gray':
                        report += `   Gray: ${swatch.color.gray}%\n`;
                        break;
                }
            }
            report += "\n";
        });

        // Download report
        const blob = new Blob([report], { type: 'text/plain;charset=utf-8' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `color-report-${new Date().toISOString().split('T')[0]}.txt`;
        link.click();

        this.setStatus("تم إنشاء تقرير الألوان");
    }

    copyColorValues() {
        if (this.selectedSwatches.length === 0) {
            this.setStatus("لا توجد ألوان للنسخ");
            return;
        }

        let colorText = "";
        this.selectedSwatches.forEach(swatch => {
            colorText += `${swatch.name}: ${this.getColorText(swatch.color).replace(/<br>/g, ', ')}\n`;
        });

        navigator.clipboard.writeText(colorText).then(() => {
            this.setStatus("تم نسخ قيم الألوان");
        }).catch(() => {
            this.setStatus("فشل في نسخ القيم");
        });
    }

    // Keyboard shortcuts
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key.toLowerCase()) {
                    case 'r':
                        e.preventDefault();
                        this.previewSwatches();
                        break;
                    case 'g':
                        e.preventDefault();
                        this.generateLegend();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.exportSettings();
                        break;
                    case 'i':
                        e.preventDefault();
                        this.importSettings();
                        break;
                }
            }
        });
    }

    // Auto-refresh functionality
    setupAutoRefresh() {
        setInterval(() => {
            if (this.settings.autoRefresh) {
                this.previewSwatches();
            }
        }, 5000); // Check every 5 seconds
    }

    // Preset configurations
    applyPreset(presetName) {
        const presets = {
            compact: {
                columns: 6,
                width: 100,
                height: 80,
                textSize: 8,
                printColors: { HEX: true, RGB: false, CMYK: false, LAB: false, GrayScale: false },
                hPadding: 5,
                vPadding: 5
            },
            detailed: {
                columns: 3,
                width: 200,
                height: 150,
                textSize: 12,
                printColors: { HEX: true, RGB: true, CMYK: true, LAB: true, GrayScale: true },
                hPadding: 15,
                vPadding: 15
            },
            minimal: {
                columns: 4,
                width: 120,
                height: 100,
                textSize: 9,
                printColors: { HEX: true, RGB: false, CMYK: false, LAB: false, GrayScale: false },
                hPadding: 8,
                vPadding: 8,
                splitComponents: false
            },
            professional: {
                columns: 4,
                width: 180,
                height: 140,
                textSize: 11,
                printColors: { HEX: true, RGB: true, CMYK: true, LAB: false, GrayScale: false },
                hPadding: 12,
                vPadding: 12,
                splitComponents: true
            }
        };

        if (presets[presetName]) {
            this.settings = { ...this.settings, ...presets[presetName] };
            this.updateUI();
            this.updatePreview();
            this.setStatus(`تم تطبيق الإعداد المسبق: ${presetName}`);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.colorSwatchUI = new ColorSwatchLegendUI();

    // Setup additional features
    window.colorSwatchUI.setupKeyboardShortcuts();
    window.colorSwatchUI.setupAutoRefresh();

    // Add context menu for advanced options
    document.addEventListener('contextmenu', (e) => {
        e.preventDefault();
        // Could add custom context menu here
    });
});

// Auto-save settings on change
window.addEventListener('beforeunload', () => {
    if (window.colorSwatchUI) {
        window.colorSwatchUI.saveSettings();
    }
});

// Handle theme changes
if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleThemeChange = (e) => {
        document.body.classList.toggle('dark-theme', e.matches);
    };

    // Use addEventListener instead of deprecated addListener
    if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', handleThemeChange);
    } else {
        // Fallback for older browsers
        mediaQuery.addListener(handleThemeChange);
    }

    // Apply initial theme
    handleThemeChange(mediaQuery);
}
