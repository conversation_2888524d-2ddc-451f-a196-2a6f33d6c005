/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 12px;
    line-height: 1.4;
    color: #333;
    background: #f5f5f5;
    direction: ltr;
}

.container {
    padding: 10px;
    max-width: 100%;
    height: 100vh;
    overflow-y: auto;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    text-align: center;
}

.header h1 {
    font-size: 18px;
    margin-bottom: 5px;
}

.header p {
    font-size: 11px;
    opacity: 0.9;
}

/* Settings Panel */
.settings-panel {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.setting-group {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.setting-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.setting-group h3 {
    font-size: 14px;
    color: #555;
    margin-bottom: 10px;
    font-weight: 600;
}

.input-group {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    gap: 10px;
}

.input-group label {
    flex: 1;
    font-size: 11px;
    color: #666;
}

.input-group input[type="number"],
.input-group input[type="text"] {
    flex: 0 0 80px;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 11px;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 11px;
    cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
    margin: 0;
}

/* Preview Panel */
.preview-panel {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.preview-panel h2 {
    font-size: 14px;
    color: #555;
    margin-bottom: 10px;
}

.preview-area {
    min-height: 150px;
    border: 2px dashed #ddd;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    color: #999;
    background: #fafafa;
}

.preview-grid {
    display: grid;
    gap: 10px;
    justify-content: center;
}

.preview-swatch {
    border-radius: 4px;
    border: 1px solid #ddd;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.preview-color {
    height: 60px;
    width: 100%;
}

.preview-text {
    padding: 8px;
    font-size: 9px;
    line-height: 1.2;
    background: white;
}

/* Action Buttons */
.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.btn {
    padding: 10px 15px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Status Bar */
.status-bar {
    background: white;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

#statusText {
    font-size: 11px;
    color: #666;
}

.progress-bar {
    width: 100px;
    height: 4px;
    background: #eee;
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #007bff;
    width: 0%;
    transition: width 0.3s ease;
    animation: progress-animation 2s infinite;
}

@keyframes progress-animation {
    0% { width: 0%; }
    50% { width: 100%; }
    100% { width: 0%; }
}

/* Responsive Design */
@media (max-width: 350px) {
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .input-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .input-group input {
        flex: none;
    }
}

/* Advanced Features */
.advanced-features {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.advanced-features h2 {
    font-size: 14px;
    color: #555;
    margin-bottom: 10px;
}

.feature-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 15px;
}

.btn-outline {
    background: transparent;
    border: 1px solid #ddd;
    color: #666;
}

.btn-outline:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

/* Quick Settings */
.quick-settings {
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.quick-settings h3 {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.preset-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px;
}

.preset-btn {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    color: #666;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.preset-btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
}

.preset-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

/* Dark Theme Support */
.dark-theme {
    background: #2d3748;
    color: #e2e8f0;
}

.dark-theme .container {
    background: #2d3748;
}

.dark-theme .settings-panel,
.dark-theme .preview-panel,
.dark-theme .advanced-features,
.dark-theme .status-bar {
    background: #4a5568;
    color: #e2e8f0;
}

.dark-theme .setting-group {
    border-bottom-color: #718096;
}

.dark-theme .input-group input {
    background: #2d3748;
    border-color: #718096;
    color: #e2e8f0;
}

.dark-theme .preview-area {
    background: #2d3748;
    border-color: #718096;
    color: #a0aec0;
}

.dark-theme .btn-outline {
    border-color: #718096;
    color: #a0aec0;
}

.dark-theme .btn-outline:hover {
    background: #2d3748;
    border-color: #63b3ed;
    color: #63b3ed;
}

.dark-theme .preset-btn {
    background: #2d3748;
    border-color: #718096;
    color: #a0aec0;
}

.dark-theme .preset-btn:hover {
    background: #4a5568;
    border-color: #63b3ed;
    color: #63b3ed;
}

/* Tooltips */
[title] {
    position: relative;
}

[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.settings-panel,
.preview-panel,
.advanced-features {
    animation: fadeIn 0.3s ease-out;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.dark-theme ::-webkit-scrollbar-track {
    background: #4a5568;
}

.dark-theme ::-webkit-scrollbar-thumb {
    background: #718096;
}

.dark-theme ::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}
