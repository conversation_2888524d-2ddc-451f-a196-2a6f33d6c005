# دليل التثبيت والاختبار - إضافة Color Swatch Legend

## متطلبات ما قبل التثبيت

### 1. تحقق من إصدار Adobe Illustrator
- Adobe Illustrator CC 2018 أو أحدث
- تأكد من تفعيل CEP Extensions

### 2. تفعيل وضع المطور
قم بتشغيل الأمر التالي في Command Prompt (Windows) أو Terminal (Mac):

**Windows:**
```cmd
reg add HKCU\Software\Adobe\CSXS.9 /v PlayerDebugMode /t REG_SZ /d 1
reg add HKCU\Software\Adobe\CSXS.10 /v PlayerDebugMode /t REG_SZ /d 1
reg add HKCU\Software\Adobe\CSXS.11 /v PlayerDebugMode /t REG_SZ /d 1
```

**macOS:**
```bash
defaults write com.adobe.CSXS.9 PlayerDebugMode 1
defaults write com.adobe.CSXS.10 PlayerDebugMode 1
defaults write com.adobe.CSXS.11 PlayerDebugMode 1
```

## خطوات التثبيت

### الطريقة 1: التثبيت اليدوي (موصى بها للتطوير)

1. **إغلاق Adobe Illustrator تماماً**

2. **نسخ الملفات:**
   انسخ مجلد `ColorSwatchLegend` بالكامل إلى مجلد الإضافات:

   **Windows:**
   ```
   C:\Program Files (x86)\Common Files\Adobe\CEP\extensions\
   ```

   **macOS:**
   ```
   /Library/Application Support/Adobe/CEP/extensions/
   ```

3. **التحقق من الأذونات:**
   - تأكد من أن المجلد والملفات قابلة للقراءة
   - في macOS، قد تحتاج لتشغيل: `sudo chmod -R 755 /Library/Application\ Support/Adobe/CEP/extensions/ColorSwatchLegend`

4. **إعادة تشغيل Illustrator**

### الطريقة 2: التثبيت للاختبار المحلي

1. **إنشاء رابط رمزي (Symbolic Link):**

   **Windows (كمدير):**
   ```cmd
   mklink /D "C:\Program Files (x86)\Common Files\Adobe\CEP\extensions\ColorSwatchLegend" "D:\استخراج اكواد الالوان استريتر\ColorSwatchLegend"
   ```

   **macOS:**
   ```bash
   sudo ln -s "/path/to/your/ColorSwatchLegend" "/Library/Application Support/Adobe/CEP/extensions/ColorSwatchLegend"
   ```

## التحقق من التثبيت

### 1. فتح الإضافة
1. افتح Adobe Illustrator
2. اذهب إلى `Window > Extensions`
3. ابحث عن "Color Swatch Legend"
4. اضغط عليها لفتح الإضافة

### 2. التحقق من عمل الإضافة
1. أنشئ مستند جديد في Illustrator
2. أضف بعض الألوان إلى لوحة الألوان (Swatches)
3. اختر عدة ألوان من اللوحة
4. في الإضافة، اضغط "معاينة"
5. يجب أن تظهر الألوان في منطقة المعاينة

## حل المشاكل الشائعة

### المشكلة: الإضافة لا تظهر في القائمة

**الحلول:**
1. تأكد من تفعيل PlayerDebugMode
2. تحقق من مسار التثبيت
3. أعد تشغيل Illustrator
4. تحقق من أذونات الملفات

**للتحقق من الأخطاء:**
- افتح Developer Console: `F12` أو `Ctrl+Shift+I`
- ابحث عن رسائل الخطأ في Console

### المشكلة: الإضافة تفتح لكن لا تعمل

**الحلول:**
1. تحقق من Console للأخطاء JavaScript
2. تأكد من وجود جميع الملفات المطلوبة
3. تحقق من صحة ملف manifest.xml

### المشكلة: خطأ "لم يتم اختيار أي عينات"

**الحلول:**
1. اختر عينات من لوحة الألوان (Swatches Panel)
2. تأكد من أن العينات من نوع مدعوم (RGB, CMYK, Gray)
3. تجنب العينات الفارغة أو التالفة

## اختبار الميزات

### 1. اختبار المعاينة
- [ ] اختيار عينات مختلفة
- [ ] تغيير الإعدادات ومشاهدة التحديث
- [ ] اختبار أنواع ألوان مختلفة (RGB, CMYK, Gray)

### 2. اختبار إنشاء المخطط
- [ ] إنشاء مخطط بسيط
- [ ] تجربة إعدادات مختلفة
- [ ] التحقق من جودة النتيجة

### 3. اختبار الميزات المتقدمة
- [ ] تصدير واستيراد الإعدادات
- [ ] استخدام الإعدادات المسبقة
- [ ] إنشاء تقرير الألوان
- [ ] نسخ قيم الألوان

### 4. اختبار الاختصارات
- [ ] Ctrl+R للمعاينة
- [ ] Ctrl+G لإنشاء المخطط
- [ ] Ctrl+E لتصدير الإعدادات
- [ ] Ctrl+I لاستيراد الإعدادات

## اختبار الأداء

### 1. اختبار مع عدد كبير من الألوان
- جرب مع 50+ عينة لون
- راقب الأداء والاستجابة
- تحقق من استهلاك الذاكرة

### 2. اختبار التوافق
- جرب مع مستندات مختلفة الأحجام
- اختبر مع أنواع ألوان مختلطة
- تحقق من العمل مع Spot Colors

## تسجيل الأخطاء

### معلومات مطلوبة عند الإبلاغ عن خطأ:
1. إصدار Adobe Illustrator
2. نظام التشغيل
3. خطوات إعادة إنتاج المشكلة
4. رسائل الخطأ من Console
5. لقطة شاشة للمشكلة

### ملفات السجل:
- Console logs من Developer Tools
- ملف manifest.xml
- إعدادات الإضافة المحفوظة

## التطوير والتخصيص

### تعديل الكود:
1. عدل الملفات في مجلد المصدر
2. أعد تحميل الإضافة: `Window > Extensions > Color Swatch Legend`
3. أو أعد تشغيل Illustrator للتغييرات الكبيرة

### إضافة ميزات جديدة:
1. عدل `js/main.js` للواجهة
2. عدل `jsx/colorSwatchLegend.jsx` لوظائف Illustrator
3. عدل `css/styles.css` للتصميم
4. عدل `index.html` لعناصر جديدة

## الدعم

للحصول على المساعدة:
1. راجع ملف README.md
2. تحقق من ملفات التوثيق
3. ابحث في مجتمع Adobe Developer
4. استخدم Adobe CEP Documentation

---

**ملاحظة مهمة:** احتفظ بنسخة احتياطية من ملفاتك قبل اختبار الإضافة لأول مرة.
